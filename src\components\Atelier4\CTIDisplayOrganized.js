import React, { useState } from 'react';
import { Save, Loader2, ChevronDown, ChevronUp, ExternalLink, Shield, Target, Bot, Database } from 'lucide-react';

const CTIDisplayOrganized = ({ results, onSave, isSaving }) => {
    const [expandedAssets, setExpandedAssets] = useState({});
    const [expandedSections, setExpandedSections] = useState({});
    const [expandedVulns, setExpandedVulns] = useState({});
    const [expandedTechniques, setExpandedTechniques] = useState({});

    if (!results || !results.assets || results.assets.length === 0) {
        return (
            <div className="text-center py-8">
                <p className="text-gray-500">Aucun résultat d'analyse CTI disponible.</p>
            </div>
        );
    }

    const toggleAssetExpansion = (assetId) => {
        setExpandedAssets(prev => ({
            ...prev,
            [assetId]: !prev[assetId]
        }));
    };

    const toggleSectionExpansion = (assetId, section) => {
        const key = `${assetId}-${section}`;
        setExpandedSections(prev => ({
            ...prev,
            [key]: !prev[key]
        }));
    };

    const toggleVulnExpansion = (vulnId) => {
        setExpandedVulns(prev => ({
            ...prev,
            [vulnId]: !prev[vulnId]
        }));
    };

    const toggleTechniqueExpansion = (techId) => {
        setExpandedTechniques(prev => ({
            ...prev,
            [techId]: !prev[techId]
        }));
    };

    // Organize techniques by source
    const organizeTechniquesBySource = (techniques) => {
        const organized = {
            mitre: [],
            atlas: []
        };
        
        techniques.forEach(tech => {
            if (tech.isAtlas || tech.source?.includes('ATLAS') || tech.id?.startsWith('AML.')) {
                organized.atlas.push(tech);
            } else {
                organized.mitre.push(tech);
            }
        });
        
        return organized;
    };

    // Organize vulnerabilities by source
    const organizeVulnerabilitiesBySource = (vulnerabilities, searchMethod) => {
        const organized = {
            nist: [],
            euvd: []
        };
        
        vulnerabilities.forEach(vuln => {
            if (searchMethod?.includes('EUVD')) {
                organized.euvd.push(vuln);
            } else {
                organized.nist.push(vuln);
            }
        });
        
        return organized;
    };

    // Helper function to safely render score
    const renderScore = (score) => {
        if (typeof score === 'object' && score !== null) {
            return score.score || score.baseScore || 'N/A';
        }
        return score || 'N/A';
    };

    // Helper function to safely render severity
    const renderSeverity = (severity) => {
        if (typeof severity === 'object' && severity !== null) {
            return severity.severity || severity.level || 'UNKNOWN';
        }
        return severity || 'UNKNOWN';
    };

    return (
        <div className="space-y-6">
            {/* Results Summary */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Résultats de l'Analyse CTI</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{results.totalVulnerabilities || 0}</div>
                        <div className="text-sm text-gray-600">Vulnérabilités</div>
                    </div>
                    <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">{results.totalAttackTechniques || 0}</div>
                        <div className="text-sm text-gray-600">Techniques d'Attaque</div>
                    </div>
                    <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{results.assets?.length || 0}</div>
                        <div className="text-sm text-gray-600">Actifs Analysés</div>
                    </div>
                </div>
            </div>

            {/* Assets Results */}
            {results.assets.map(asset => (
                <div key={asset.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                    <div
                        className="bg-gray-50 px-6 py-4 border-b border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors"
                        onClick={() => toggleAssetExpansion(asset.id)}
                    >
                        <div className="flex justify-between items-center">
                            <div>
                                <h4 className="text-lg font-semibold text-gray-800">{asset.name}</h4>
                                <p className="text-sm text-gray-600">{asset.type}</p>
                                <div className="flex items-center space-x-4 mt-2">
                                    {asset.vulnerabilities?.length > 0 && (
                                        <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">
                                            {asset.vulnerabilities.length} vulnérabilités
                                        </span>
                                    )}
                                    {asset.attackTechniques?.length > 0 && (
                                        <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
                                            {asset.attackTechniques.length} techniques
                                        </span>
                                    )}
                                    {asset.searchMethod && asset.searchMethod !== 'none' && (
                                        <span className={`text-xs px-2 py-1 rounded-full ${
                                            asset.searchMethod.includes('NIST') ? 'bg-blue-100 text-blue-700' :
                                            asset.searchMethod.includes('EUVD') ? 'bg-indigo-100 text-indigo-700' :
                                            asset.searchMethod.includes('CPE') ? 'bg-cyan-100 text-cyan-700' :
                                            asset.searchMethod.includes('Keyword') ? 'bg-green-100 text-green-700' :
                                            asset.searchMethod.includes('Text') ? 'bg-emerald-100 text-emerald-700' :
                                            asset.searchMethod.includes('+') ? 'bg-yellow-100 text-yellow-700' :
                                            'bg-gray-100 text-gray-700'
                                        }`}>
                                            {asset.searchMethod.includes('NIST CPE') ? '🎯 NIST CPE' :
                                             asset.searchMethod.includes('NIST Keyword') ? '🔍 NIST Mots-clés' :
                                             asset.searchMethod.includes('EUVD Vendor+Product') ? '🎯 EUVD Vendeur+Produit' :
                                             asset.searchMethod.includes('EUVD Text') ? '🔍 EUVD Texte' :
                                             asset.searchMethod.includes('+') ? '🔄 Hybride' :
                                             asset.searchMethod}
                                        </span>
                                    )}
                                </div>
                            </div>
                            {expandedAssets[asset.id] ?
                                <ChevronUp className="h-5 w-5 text-gray-500" /> :
                                <ChevronDown className="h-5 w-5 text-gray-500" />
                            }
                        </div>
                    </div>

                    {expandedAssets[asset.id] && (
                        <div className="p-6 space-y-4">
                            {/* Organize data by source */}
                            {(() => {
                                const organizedVulns = organizeVulnerabilitiesBySource(asset.vulnerabilities || [], asset.searchMethod);
                                const organizedTechs = organizeTechniquesBySource(asset.attackTechniques || []);
                                
                                return (
                                    <div className="space-y-4">
                                        {/* NIST Vulnerabilities Section */}
                                        {organizedVulns.nist.length > 0 && (
                                            <div className="border border-blue-200 rounded-lg">
                                                <div 
                                                    className="bg-blue-50 px-4 py-3 cursor-pointer hover:bg-blue-100 transition-colors flex justify-between items-center"
                                                    onClick={() => toggleSectionExpansion(asset.id, 'nist-vulns')}
                                                >
                                                    <div className="flex items-center">
                                                        <Shield className="h-4 w-4 mr-2 text-blue-600" />
                                                        <span className="font-semibold text-blue-800">Vulnérabilités NIST ({organizedVulns.nist.length})</span>
                                                    </div>
                                                    {expandedSections[`${asset.id}-nist-vulns`] ? 
                                                        <ChevronUp className="h-4 w-4 text-blue-600" /> : 
                                                        <ChevronDown className="h-4 w-4 text-blue-600" />
                                                    }
                                                </div>
                                                {expandedSections[`${asset.id}-nist-vulns`] && (
                                                    <div className="p-4 space-y-2">
                                                        {organizedVulns.nist.map(vuln => (
                                                            <VulnerabilityCard 
                                                                key={vuln.id} 
                                                                vuln={vuln} 
                                                                expanded={expandedVulns[vuln.id]}
                                                                onToggle={() => toggleVulnExpansion(vuln.id)}
                                                                renderScore={renderScore}
                                                                renderSeverity={renderSeverity}
                                                            />
                                                        ))}
                                                    </div>
                                                )}
                                            </div>
                                        )}

                                        {/* EUVD Vulnerabilities Section */}
                                        {organizedVulns.euvd.length > 0 && (
                                            <div className="border border-indigo-200 rounded-lg">
                                                <div 
                                                    className="bg-indigo-50 px-4 py-3 cursor-pointer hover:bg-indigo-100 transition-colors flex justify-between items-center"
                                                    onClick={() => toggleSectionExpansion(asset.id, 'euvd-vulns')}
                                                >
                                                    <div className="flex items-center">
                                                        <Database className="h-4 w-4 mr-2 text-indigo-600" />
                                                        <span className="font-semibold text-indigo-800">Vulnérabilités EUVD ({organizedVulns.euvd.length})</span>
                                                    </div>
                                                    {expandedSections[`${asset.id}-euvd-vulns`] ? 
                                                        <ChevronUp className="h-4 w-4 text-indigo-600" /> : 
                                                        <ChevronDown className="h-4 w-4 text-indigo-600" />
                                                    }
                                                </div>
                                                {expandedSections[`${asset.id}-euvd-vulns`] && (
                                                    <div className="p-4 space-y-2">
                                                        {organizedVulns.euvd.map(vuln => (
                                                            <VulnerabilityCard 
                                                                key={vuln.id} 
                                                                vuln={vuln} 
                                                                expanded={expandedVulns[vuln.id]}
                                                                onToggle={() => toggleVulnExpansion(vuln.id)}
                                                                renderScore={renderScore}
                                                                renderSeverity={renderSeverity}
                                                            />
                                                        ))}
                                                    </div>
                                                )}
                                            </div>
                                        )}

                                        {/* MITRE ATT&CK Techniques Section */}
                                        {organizedTechs.mitre.length > 0 && (
                                            <div className="border border-red-200 rounded-lg">
                                                <div 
                                                    className="bg-red-50 px-4 py-3 cursor-pointer hover:bg-red-100 transition-colors flex justify-between items-center"
                                                    onClick={() => toggleSectionExpansion(asset.id, 'mitre-techs')}
                                                >
                                                    <div className="flex items-center">
                                                        <Target className="h-4 w-4 mr-2 text-red-600" />
                                                        <span className="font-semibold text-red-800">Techniques MITRE ATT&CK ({organizedTechs.mitre.length})</span>
                                                    </div>
                                                    {expandedSections[`${asset.id}-mitre-techs`] ? 
                                                        <ChevronUp className="h-4 w-4 text-red-600" /> : 
                                                        <ChevronDown className="h-4 w-4 text-red-600" />
                                                    }
                                                </div>
                                                {expandedSections[`${asset.id}-mitre-techs`] && (
                                                    <div className="p-4 space-y-2">
                                                        {organizedTechs.mitre.map(technique => (
                                                            <TechniqueCard 
                                                                key={technique.id} 
                                                                technique={technique} 
                                                                expanded={expandedTechniques[technique.id]}
                                                                onToggle={() => toggleTechniqueExpansion(technique.id)}
                                                                color="red"
                                                            />
                                                        ))}
                                                    </div>
                                                )}
                                            </div>
                                        )}

                                        {/* MITRE ATLAS Techniques Section */}
                                        {organizedTechs.atlas.length > 0 && (
                                            <div className="border border-purple-200 rounded-lg">
                                                <div 
                                                    className="bg-purple-50 px-4 py-3 cursor-pointer hover:bg-purple-100 transition-colors flex justify-between items-center"
                                                    onClick={() => toggleSectionExpansion(asset.id, 'atlas-techs')}
                                                >
                                                    <div className="flex items-center">
                                                        <Bot className="h-4 w-4 mr-2 text-purple-600" />
                                                        <span className="font-semibold text-purple-800">Techniques MITRE ATLAS ({organizedTechs.atlas.length})</span>
                                                    </div>
                                                    {expandedSections[`${asset.id}-atlas-techs`] ? 
                                                        <ChevronUp className="h-4 w-4 text-purple-600" /> : 
                                                        <ChevronDown className="h-4 w-4 text-purple-600" />
                                                    }
                                                </div>
                                                {expandedSections[`${asset.id}-atlas-techs`] && (
                                                    <div className="p-4 space-y-2">
                                                        {organizedTechs.atlas.map(technique => (
                                                            <TechniqueCard 
                                                                key={technique.id} 
                                                                technique={technique} 
                                                                expanded={expandedTechniques[technique.id]}
                                                                onToggle={() => toggleTechniqueExpansion(technique.id)}
                                                                color="purple"
                                                            />
                                                        ))}
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                );
                            })()}
                        </div>
                    )}
                </div>
            ))}

            {/* Save Button */}
            <div className="flex justify-end">
                <button
                    onClick={onSave}
                    disabled={isSaving}
                    className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    {isSaving ? (
                        <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Sauvegarde...
                        </>
                    ) : (
                        <>
                            <Save className="h-4 w-4 mr-2" />
                            Sauvegarder les Résultats
                        </>
                    )}
                </button>
            </div>
        </div>
    );
};

// Vulnerability Card Component
const VulnerabilityCard = ({ vuln, expanded, onToggle, renderScore, renderSeverity }) => (
    <div className="bg-red-50 border border-red-200 rounded-lg">
        <div
            className="p-4 cursor-pointer hover:bg-red-100 transition-colors"
            onClick={onToggle}
        >
            <div className="flex justify-between items-start">
                <div className="flex-1">
                    <div className="flex items-center justify-between">
                        <h6 className="font-semibold text-red-800">{vuln.id}</h6>
                        {expanded ?
                            <ChevronUp className="h-4 w-4 text-red-600" /> :
                            <ChevronDown className="h-4 w-4 text-red-600" />
                        }
                    </div>
                    <div className="flex items-center mt-2 space-x-4">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                            renderSeverity(vuln.severity) === 'CRITICAL' ? 'bg-red-100 text-red-800' :
                            renderSeverity(vuln.severity) === 'HIGH' ? 'bg-orange-100 text-orange-800' :
                            renderSeverity(vuln.severity) === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                        }`}>
                            {renderSeverity(vuln.severity)}
                        </span>
                        <span className="text-xs text-gray-500">
                            Score: {renderScore(vuln.severity)}
                        </span>
                        {vuln.epssScore && (
                            <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">
                                EPSS: {vuln.epssScore}%
                            </span>
                        )}
                        {vuln.exploited && (
                            <span className="text-xs bg-red-600 text-white px-2 py-1 rounded">
                                Exploité
                            </span>
                        )}
                    </div>
                </div>
            </div>
        </div>
        {expanded && (
            <div className="px-4 pb-4 border-t border-red-200">
                <div className="mt-3 space-y-3">
                    <p className="text-sm text-gray-700">{vuln.description}</p>
                    
                    <div className="grid grid-cols-2 gap-4 text-xs">
                        {vuln.publishedDate && (
                            <div>
                                <span className="font-semibold text-gray-600">Publié:</span>
                                <span className="ml-2">{new Date(vuln.publishedDate).toLocaleDateString()}</span>
                            </div>
                        )}
                        {vuln.lastModified && (
                            <div>
                                <span className="font-semibold text-gray-600">Modifié:</span>
                                <span className="ml-2">{new Date(vuln.lastModified).toLocaleDateString()}</span>
                            </div>
                        )}
                    </div>
                    
                    {vuln.references && Array.isArray(vuln.references) && vuln.references.length > 0 && (
                        <div>
                            <div className="text-xs font-semibold text-gray-600 mb-1">Références:</div>
                            <div className="space-y-1">
                                {vuln.references.map((ref, idx) => (
                                    <a
                                        key={idx}
                                        href={ref.url || ref}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="block text-xs text-blue-600 hover:text-blue-800 truncate"
                                    >
                                        {ref.name || ref.url || ref}
                                    </a>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        )}
    </div>
);

// Technique Card Component
const TechniqueCard = ({ technique, expanded, onToggle, color }) => {
    const colorClasses = {
        red: {
            bg: 'bg-red-50',
            border: 'border-red-200',
            hover: 'hover:bg-red-100',
            text: 'text-red-800',
            icon: 'text-red-600',
            badge: 'bg-red-100 text-red-700'
        },
        purple: {
            bg: 'bg-purple-50',
            border: 'border-purple-200',
            hover: 'hover:bg-purple-100',
            text: 'text-purple-800',
            icon: 'text-purple-600',
            badge: 'bg-purple-100 text-purple-700'
        }
    };
    
    const classes = colorClasses[color] || colorClasses.red;
    
    return (
        <div className={`${classes.bg} ${classes.border} border rounded-lg`}>
            <div
                className={`p-4 cursor-pointer ${classes.hover} transition-colors`}
                onClick={onToggle}
            >
                <div className="flex justify-between items-start">
                    <div className="flex-1">
                        <div className="flex items-center justify-between">
                            <h6 className={`font-semibold ${classes.text}`}>
                                {technique.id} - {technique.name}
                            </h6>
                            {expanded ?
                                <ChevronUp className={`h-4 w-4 ${classes.icon}`} /> :
                                <ChevronDown className={`h-4 w-4 ${classes.icon}`} />
                            }
                        </div>
                        <div className="flex items-center mt-2 space-x-4">
                            <span className={`px-2 py-1 rounded text-xs font-medium ${classes.badge}`}>
                                {technique.tactic}
                            </span>
                            {technique.platforms && technique.platforms.length > 0 && (
                                <span className="text-xs text-gray-600">
                                    Plateformes: {technique.platforms.join(', ')}
                                </span>
                            )}
                            {technique.source && (
                                <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                                    {technique.source}
                                </span>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            {expanded && (
                <div className={`px-4 pb-4 border-t ${classes.border}`}>
                    <div className="mt-3 space-y-3">
                        <p className="text-sm text-gray-700">{technique.description}</p>
                        
                        <div className="grid grid-cols-2 gap-4">
                            {technique.dataSource && technique.dataSource.length > 0 && (
                                <div>
                                    <div className="text-xs font-semibold text-gray-600 mb-1">Sources de Données:</div>
                                    <div className="text-xs text-gray-700">
                                        {Array.isArray(technique.dataSource) ?
                                            technique.dataSource.join(', ') :
                                            technique.dataSource
                                        }
                                    </div>
                                </div>
                            )}
                            {technique.url && (
                                <div>
                                    <div className="text-xs font-semibold text-gray-600 mb-1">Référence:</div>
                                    <a
                                        href={technique.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
                                    >
                                        {technique.isAtlas ? 'MITRE ATLAS' : 'MITRE ATT&CK'} <ExternalLink className="h-3 w-3 ml-1" />
                                    </a>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default CTIDisplayOrganized;
