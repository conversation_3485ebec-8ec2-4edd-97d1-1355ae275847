// src/services/ctiService.js
// Real CTI Analysis Service using NIST NVD and MITRE ATT&CK APIs

class CTIService {
  constructor() {
    // Use backend proxy URLs instead of direct API calls
    this.nistBaseUrl = '/api/nist/search'; // Backend proxy for NIST
    this.euvdBaseUrl = '/api/euvd/search'; // Backend proxy for EUVD
    this.mitreBaseUrl = 'https://attack.mitre.org/api/v2';
    this.mitreStixUrl = 'https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json';

    // Local MITRE API servers (through backend proxy)
    this.localMitreAttackUrl = '/api/mitre/local-attack';
    this.localMitreAtlasUrl = '/api/mitre/local-atlas';

    // Rate limiting for APIs
    this.requestQueue = [];
    this.isProcessing = false;

    // Cache for MITRE data (refresh every 24 hours)
    this.mitreCache = {
      data: null,
      lastUpdated: null,
      cacheExpiry: 24 * 60 * 60 * 1000 // 24 hours in milliseconds
    };
  }

  // Generate CPE (Common Platform Enumeration) string from asset info
  generateCPE(asset) {
    if (asset.vendor && asset.product) {
      // Format: cpe:2.3:a:vendor:product:version:*:*:*:*:*:*:*
      const vendor = asset.vendor.toLowerCase().replace(/\s+/g, '_');
      const product = asset.product.toLowerCase().replace(/\s+/g, '_');
      const version = asset.version || '*';
      return `cpe:2.3:a:${vendor}:${product}:${version}:*:*:*:*:*:*:*`;
    }
    return null;
  }

  // Search for vulnerabilities using NIST or EUVD API based on analysis type
  async searchVulnerabilities(asset, analysisType = 'euvd') {
    try {
      console.log(`[CTI] Searching vulnerabilities for asset: ${asset.name} using ${analysisType}`);

      // Route to appropriate API based on analysis type
      if (analysisType === 'nist' || analysisType === 'combined') {
        return await this.searchNISTVulnerabilities(asset);
      } else if (analysisType === 'euvd') {
        return await this.searchEUVDVulnerabilities(asset);
      }

      return {
        vulnerabilities: [],
        searchMethod: 'unsupported',
        searchSuccess: false
      };

    } catch (error) {
      console.error(`[CTI] Error searching vulnerabilities for ${asset.name}:`, error);
      return {
        vulnerabilities: [],
        searchMethod: 'error',
        searchSuccess: false
      };
    }
  }

  // Search for vulnerabilities using NIST NVD API with keyword search
  async searchNISTVulnerabilities(asset) {
    try {
      console.log(`[CTI] NIST search for asset: ${asset.name}`);

      let allVulnerabilities = [];
      let searchMethod = 'none';

      // Strategy 1: CPE-based search (primary method)
      const cpe = this.generateCPE(asset);
      if (cpe) {
        console.log(`[CTI] Strategy 1: NIST CPE-based search with: ${cpe}`);
        const cpeResults = await this.searchByNISTCPE(asset, cpe);
        if (cpeResults.length > 0) {
          allVulnerabilities = cpeResults;
          searchMethod = 'NIST CPE';
          console.log(`[CTI] NIST CPE search successful: ${cpeResults.length} vulnerabilities found`);
        } else {
          console.log(`[CTI] NIST CPE search yielded no results, trying keyword search...`);
        }
      } else {
        console.log(`[CTI] Cannot generate CPE for asset: ${asset.name}, using keyword search`);
      }

      // Strategy 2: NIST keyword search (fallback)
      if (allVulnerabilities.length === 0) {
        const keywords = this.generateKeywords(asset);
        if (keywords.length > 0) {
          console.log(`[CTI] Strategy 2: NIST keyword search with: ${keywords.join(', ')}`);

          // Try different keyword combinations
          for (const keywordSet of keywords) {
            const keywordResults = await this.searchByNISTKeywords(asset, keywordSet);
            if (keywordResults.length > 0) {
              allVulnerabilities = [...allVulnerabilities, ...keywordResults];
              searchMethod = searchMethod === 'none' ? 'NIST Keyword' : `${searchMethod} + Keyword`;
              console.log(`[CTI] NIST keyword search "${keywordSet}" found ${keywordResults.length} vulnerabilities`);
              break; // Use first successful keyword search
            }
          }
        }
      }

      // Remove duplicates based on CVE ID
      const uniqueVulnerabilities = this.deduplicateVulnerabilities(allVulnerabilities);

      console.log(`[CTI] NIST final results for ${asset.name}: ${uniqueVulnerabilities.length} unique vulnerabilities (Method: ${searchMethod})`);
      return {
        vulnerabilities: uniqueVulnerabilities,
        searchMethod: searchMethod,
        searchSuccess: uniqueVulnerabilities.length > 0
      };

    } catch (error) {
      console.error(`[CTI] Error in NIST search for ${asset.name}:`, error);
      return {
        vulnerabilities: [],
        searchMethod: 'error',
        searchSuccess: false
      };
    }
  }

  // Search for vulnerabilities using EUVD API with keyword search
  async searchEUVDVulnerabilities(asset) {
    try {
      console.log(`[CTI] EUVD search for asset: ${asset.name}`);

      let allVulnerabilities = [];
      let searchMethod = 'none';

      // Strategy 1: EUVD vendor + product search (primary method)
      if (asset.vendor && asset.product) {
        console.log(`[CTI] Strategy 1: EUVD vendor+product search with: ${asset.vendor} ${asset.product}`);
        const vendorProductResults = await this.searchByEUVDVendorProduct(asset);
        if (vendorProductResults.length > 0) {
          allVulnerabilities = vendorProductResults;
          searchMethod = 'EUVD Vendor+Product';
          console.log(`[CTI] EUVD vendor+product search successful: ${vendorProductResults.length} vulnerabilities found`);
        } else {
          console.log(`[CTI] EUVD vendor+product search yielded no results, trying text keyword search...`);
        }
      } else {
        console.log(`[CTI] Missing vendor/product for asset: ${asset.name}, using text keyword search`);
      }

      // Strategy 2: EUVD text keyword search (fallback)
      if (allVulnerabilities.length === 0) {
        const keywords = this.generateKeywords(asset);
        if (keywords.length > 0) {
          console.log(`[CTI] Strategy 2: EUVD text keyword search with: ${keywords.join(', ')}`);

          // Try different keyword combinations
          for (const keywordSet of keywords) {
            const keywordResults = await this.searchByEUVDText(asset, keywordSet);
            if (keywordResults.length > 0) {
              allVulnerabilities = [...allVulnerabilities, ...keywordResults];
              searchMethod = searchMethod === 'none' ? 'EUVD Text' : `${searchMethod} + Text`;
              console.log(`[CTI] EUVD text search "${keywordSet}" found ${keywordResults.length} vulnerabilities`);
              break; // Use first successful keyword search
            }
          }
        }
      }

      // Remove duplicates based on CVE ID
      const uniqueVulnerabilities = this.deduplicateVulnerabilities(allVulnerabilities);

      console.log(`[CTI] EUVD final results for ${asset.name}: ${uniqueVulnerabilities.length} unique vulnerabilities (Method: ${searchMethod})`);
      return {
        vulnerabilities: uniqueVulnerabilities,
        searchMethod: searchMethod,
        searchSuccess: uniqueVulnerabilities.length > 0
      };

    } catch (error) {
      console.error(`[CTI] Error in EUVD search for ${asset.name}:`, error);
      return {
        vulnerabilities: [],
        searchMethod: 'error',
        searchSuccess: false
      };
    }
  }

  // EUVD vendor + product search method
  async searchByEUVDVendorProduct(asset) {
    try {
      const params = new URLSearchParams({
        vendor: asset.vendor,
        product: asset.product,
        size: '20', // Max 100, using 20 for reasonable response time
        page: '0'
      });

      console.log(`[CTI] EUVD vendor+product search URL: ${this.euvdBaseUrl}?${params}`);

      const response = await fetch(`${this.euvdBaseUrl}?${params}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.error(`[CTI] EUVD vendor+product API error: ${response.status} ${response.statusText}`);
        return [];
      }

      const data = await response.json();
      console.log(`[CTI] EUVD vendor+product response for "${asset.vendor} ${asset.product}": ${data.totalElements || data.length || 0} vulnerabilities`);
      return this.parseEUVDVulnerabilities(data);
    } catch (error) {
      console.error(`[CTI] EUVD vendor+product search failed for ${asset.name}:`, error);
      return [];
    }
  }

  // EUVD text keyword search method
  async searchByEUVDText(asset, keywords) {
    try {
      const params = new URLSearchParams({
        text: keywords, // EUVD text parameter for keyword search
        size: '15', // Reasonable size for keyword search
        page: '0'
      });

      console.log(`[CTI] EUVD text search URL: ${this.euvdBaseUrl}?${params}`);

      const response = await fetch(`${this.euvdBaseUrl}?${params}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.error(`[CTI] EUVD text API error: ${response.status} ${response.statusText}`);
        return [];
      }

      const data = await response.json();
      console.log(`[CTI] EUVD text search response for "${keywords}": ${data.totalElements || data.length || 0} vulnerabilities`);
      return this.parseEUVDVulnerabilities(data);
    } catch (error) {
      console.error(`[CTI] EUVD text search failed for ${asset.name} with keywords "${keywords}":`, error);
      return [];
    }
  }

  // NIST CPE-based search method
  async searchByNISTCPE(asset, cpe) {
    try {
      // For CPE search, we need to use the cpe-search endpoint
      const params = new URLSearchParams({
        cpeName: cpe,
        resultsPerPage: '20',
        startIndex: '0'
      });

      const response = await fetch(`/api/nist/cpe-search?${params}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.error(`[CTI] NIST CPE API error: ${response.status} ${response.statusText}`);
        return [];
      }

      const data = await response.json();
      return this.parseNISTVulnerabilities(data);
    } catch (error) {
      console.error(`[CTI] NIST CPE search failed for ${asset.name}:`, error);
      return [];
    }
  }

  // NIST keyword-based search method
  async searchByNISTKeywords(asset, keywords) {
    try {
      const params = new URLSearchParams({
        keywordSearch: keywords, // NIST keywordSearch parameter
        resultsPerPage: '15',
        startIndex: '0'
      });

      console.log(`[CTI] NIST keyword search URL: ${this.nistBaseUrl}?${params}`);

      const response = await fetch(`${this.nistBaseUrl}?${params}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.error(`[CTI] NIST keyword API error: ${response.status} ${response.statusText}`);
        return [];
      }

      const data = await response.json();
      console.log(`[CTI] NIST keyword search response for "${keywords}": ${data.totalResults || 0} total, ${data.vulnerabilities?.length || 0} returned`);
      return this.parseNISTVulnerabilities(data);
    } catch (error) {
      console.error(`[CTI] NIST keyword search failed for ${asset.name} with keywords "${keywords}":`, error);
      return [];
    }
  }

  // Generate keyword search terms from asset information based on product names
  generateKeywords(asset) {
    const keywords = [];

    // Strategy 1: Vendor + Product (most specific)
    if (asset.vendor && asset.product) {
      keywords.push(`${asset.vendor} ${asset.product}`);
    }

    // Strategy 2: Product only (broader search)
    if (asset.product) {
      keywords.push(asset.product);
    }

    // Strategy 3: Vendor only (very broad)
    if (asset.vendor) {
      keywords.push(asset.vendor);
    }

    // Strategy 4: Extract product names from asset name using enhanced patterns
    if (asset.name) {
      const productKeywords = this.extractProductKeywordsForVulnSearch(asset);
      keywords.push(...productKeywords);
    }

    // Strategy 5: Common software terms for email servers
    if (asset.name && (asset.name.toLowerCase().includes('email') || asset.name.toLowerCase().includes('mail'))) {
      const emailKeywords = ['postfix', 'sendmail', 'exim', 'qmail', 'dovecot', 'exchange'];
      emailKeywords.forEach(keyword => {
        if (!keywords.includes(keyword)) {
          keywords.push(keyword);
        }
      });
    }

    // Remove duplicates and empty strings
    const uniqueKeywords = [...new Set(keywords)].filter(k => k && k.trim().length > 0);

    console.log(`[CTI] Generated vulnerability search keywords for ${asset.name}:`, uniqueKeywords);
    return uniqueKeywords;
  }

  // Extract product keywords specifically for vulnerability searches
  extractProductKeywordsForVulnSearch(asset) {
    const keywords = [];

    if (!asset || !asset.name) return keywords;

    const assetName = asset.name.toLowerCase();

    // Product detection patterns for vulnerability searches
    const vulnProductPatterns = {
      // Web servers
      'apache': ['apache', 'httpd'],
      'nginx': ['nginx'],
      'iis': ['iis', 'internet information services'],
      'tomcat': ['tomcat', 'apache tomcat'],

      // Databases
      'mysql': ['mysql'],
      'postgresql': ['postgresql', 'postgres'],
      'mongodb': ['mongodb', 'mongo'],
      'oracle': ['oracle database', 'oracle'],
      'sql server': ['sql server', 'microsoft sql'],

      // Operating Systems
      'windows': ['windows', 'microsoft windows'],
      'linux': ['linux'],
      'ubuntu': ['ubuntu'],
      'centos': ['centos'],
      'debian': ['debian'],
      'redhat': ['redhat', 'red hat'],

      // Applications
      'wordpress': ['wordpress'],
      'drupal': ['drupal'],
      'joomla': ['joomla'],
      'sharepoint': ['sharepoint', 'microsoft sharepoint'],
      'exchange': ['exchange', 'microsoft exchange'],
      'outlook': ['outlook', 'microsoft outlook'],

      // Network equipment
      'cisco': ['cisco'],
      'juniper': ['juniper'],
      'fortinet': ['fortinet', 'fortigate'],
      'palo alto': ['palo alto'],

      // Virtualization
      'vmware': ['vmware', 'vsphere', 'vcenter'],
      'docker': ['docker'],
      'kubernetes': ['kubernetes', 'k8s']
    };

    // Check for product matches in asset name
    Object.entries(vulnProductPatterns).forEach(([product, terms]) => {
      if (assetName.includes(product)) {
        keywords.push(...terms);
        console.log(`[CTI] Detected product '${product}' for vulnerability search in asset '${asset.name}'`);
      }
    });

    // Extract meaningful words from asset name (fallback)
    const nameWords = assetName
      .split(/[\s\-_.,;:()\[\]{}]+/)
      .filter(word =>
        word.length > 2 &&
        !['le', 'la', 'les', 'de', 'du', 'des', 'et', 'ou', 'un', 'une', 'serveur', 'server', 'système', 'system'].includes(word.toLowerCase())
      )
      .slice(0, 3); // Limit to first 3 meaningful words

    keywords.push(...nameWords);

    // Final fallback for vulnerability searches: ensure we have at least some keywords
    if (keywords.length === 0) {
      console.log(`[CTI] No vulnerability search keywords generated for asset '${asset.name}', using generic fallback`);

      // Generic fallback keywords for vulnerability searches
      const genericVulnKeywords = ['software', 'application', 'system', 'service'];
      keywords.push(...genericVulnKeywords);

      // Add asset type as keyword if available
      if (asset.type) {
        keywords.push(asset.type.toLowerCase());
      }
    }

    // Remove duplicates and log final result
    const finalKeywords = [...new Set(keywords)];
    console.log(`[CTI] Final vulnerability search keywords for '${asset.name}':`, finalKeywords);

    return finalKeywords;
  }

  // Parse vulnerabilities from EUVD API response
  parseEUVDVulnerabilities(data) {
    console.log(`[CTI] Parsing EUVD response:`, data);

    // EUVD returns either an array directly or an object with content/elements/items
    const vulnerabilities = data.items || data.content || data.elements || (Array.isArray(data) ? data : []);

    console.log(`[CTI] Extracted vulnerabilities array:`, vulnerabilities);

    if (vulnerabilities && vulnerabilities.length > 0) {
      const parsed = vulnerabilities.map(vuln => {
        // Ensure references is always an array
        let references = [];
        if (vuln.references) {
          if (Array.isArray(vuln.references)) {
            references = vuln.references.slice(0, 3);
          } else if (typeof vuln.references === 'string') {
            // If references is a string, create an array with one item
            references = [{ url: vuln.references, name: 'Reference' }];
          } else if (typeof vuln.references === 'object') {
            // If references is an object, wrap it in an array
            references = [vuln.references];
          }
        }

        return {
          id: vuln.cveId || vuln.id,
          description: vuln.description || vuln.summary || 'No description available',
          severity: this.extractEUVDSeverity(vuln),
          publishedDate: vuln.publishedDate || vuln.datePublished,
          lastModified: vuln.lastModifiedDate || vuln.dateModified || vuln.dateUpdated,
          references: references,
          // EUVD specific fields
          cvssScore: vuln.cvssScore || vuln.baseScore || vuln.score,
          epssScore: vuln.epssScore,
          exploited: vuln.exploited,
          assigner: vuln.assigner,
          vendor: vuln.vendor,
          product: vuln.product
        };
      });

      console.log(`[CTI] Parsed ${parsed.length} vulnerabilities:`, parsed);
      return parsed;
    }

    console.log(`[CTI] No vulnerabilities found in response`);
    return [];
  }

  // Extract severity from EUVD vulnerability data
  extractEUVDSeverity(vuln) {
    const score = vuln.cvssScore || vuln.baseScore || vuln.score || 0;
    let severity = 'UNKNOWN';

    // Map CVSS score to severity
    if (score >= 9.0) severity = 'CRITICAL';
    else if (score >= 7.0) severity = 'HIGH';
    else if (score >= 4.0) severity = 'MEDIUM';
    else if (score > 0) severity = 'LOW';

    console.log(`[CTI] Extracted severity for ${vuln.id || 'unknown'}: score=${score}, severity=${severity}`);

    return {
      score: score,
      severity: severity,
      vector: vuln.cvssVector || vuln.vector || ''
    };
  }

  // Parse vulnerabilities from NIST API response
  parseNISTVulnerabilities(data) {
    if (data.vulnerabilities && data.vulnerabilities.length > 0) {
      return data.vulnerabilities.map(vuln => ({
        id: vuln.cve.id,
        description: vuln.cve.descriptions.find(d => d.lang === 'en')?.value || 'No description',
        severity: this.extractSeverity(vuln.cve.metrics),
        publishedDate: vuln.cve.published,
        lastModified: vuln.cve.lastModified,
        references: vuln.cve.references?.slice(0, 3) || []
      }));
    }
    return [];
  }

  // Remove duplicate vulnerabilities based on CVE ID
  deduplicateVulnerabilities(vulnerabilities) {
    const seen = new Set();
    return vulnerabilities.filter(vuln => {
      if (seen.has(vuln.id)) {
        return false;
      }
      seen.add(vuln.id);
      return true;
    });
  }

  // Extract severity from CVSS metrics
  extractSeverity(metrics) {
    if (metrics?.cvssMetricV31 && metrics.cvssMetricV31.length > 0) {
      return {
        score: metrics.cvssMetricV31[0].cvssData.baseScore,
        severity: metrics.cvssMetricV31[0].cvssData.baseSeverity,
        vector: metrics.cvssMetricV31[0].cvssData.vectorString
      };
    }
    if (metrics?.cvssMetricV30 && metrics.cvssMetricV30.length > 0) {
      return {
        score: metrics.cvssMetricV30[0].cvssData.baseScore,
        severity: metrics.cvssMetricV30[0].cvssData.baseSeverity,
        vector: metrics.cvssMetricV30[0].cvssData.vectorString
      };
    }
    if (metrics?.cvssMetricV2 && metrics.cvssMetricV2.length > 0) {
      return {
        score: metrics.cvssMetricV2[0].cvssData.baseScore,
        severity: this.mapV2Severity(metrics.cvssMetricV2[0].cvssData.baseScore),
        vector: metrics.cvssMetricV2[0].cvssData.vectorString
      };
    }
    return { score: 0, severity: 'UNKNOWN', vector: '' };
  }

  // Map CVSS v2 score to severity
  mapV2Severity(score) {
    if (score >= 9.0) return 'CRITICAL';
    if (score >= 7.0) return 'HIGH';
    if (score >= 4.0) return 'MEDIUM';
    return 'LOW';
  }

  // Enhanced MITRE ATT&CK techniques database
  getEnhancedTechniqueDatabase() {
    const database = {
      // Reconnaissance Phase Techniques
      reconnaissance: [
        { id: 'T1595.002', name: 'Vulnerability Scanning', tactic: 'Reconnaissance', description: 'Adversaries may scan victims for vulnerabilities that can be used during targeting.' },
        { id: 'T1592.002', name: 'Software', tactic: 'Reconnaissance', description: 'Adversaries may gather information about the victim\'s host software that can be used during targeting.' },
        { id: 'T1590.005', name: 'IP Addresses', tactic: 'Reconnaissance', description: 'Adversaries may gather the victim\'s IP addresses that can be used during targeting.' },
        { id: 'T1590.002', name: 'DNS', tactic: 'Reconnaissance', description: 'Adversaries may gather information about the victim\'s DNS that can be used during targeting.' },
        { id: 'T1589.002', name: 'Email Addresses', tactic: 'Reconnaissance', description: 'Adversaries may gather email addresses that can be used during targeting.' }
      ],

      // Initial Access Techniques
      initialAccess: [
        { id: 'T1190', name: 'Exploit Public-Facing Application', tactic: 'Initial Access', description: 'Adversaries may attempt to take advantage of a weakness in an Internet-facing computer or program.' },
        { id: 'T1566.001', name: 'Spearphishing Attachment', tactic: 'Initial Access', description: 'Adversaries may send spearphishing emails with a malicious attachment.' },
        { id: 'T1566.002', name: 'Spearphishing Link', tactic: 'Initial Access', description: 'Adversaries may send spearphishing emails with a malicious link.' },
        { id: 'T1078.004', name: 'Cloud Accounts', tactic: 'Initial Access', description: 'Adversaries may obtain and abuse credentials of a cloud account.' },
        { id: 'T1133', name: 'External Remote Services', tactic: 'Initial Access', description: 'Adversaries may leverage external-facing remote services to initially access.' }
      ],

      // Execution Techniques
      execution: [
        { id: 'T1059.001', name: 'PowerShell', tactic: 'Execution', description: 'Adversaries may abuse PowerShell commands and scripts for execution.' },
        { id: 'T1059.003', name: 'Windows Command Shell', tactic: 'Execution', description: 'Adversaries may abuse the Windows command shell for execution.' },
        { id: 'T1059.004', name: 'Unix Shell', tactic: 'Execution', description: 'Adversaries may abuse Unix shell commands and scripts for execution.' },
        { id: 'T1203', name: 'Exploitation for Client Execution', tactic: 'Execution', description: 'Adversaries may exploit software vulnerabilities in client applications to execute code.' },
        { id: 'T1204.002', name: 'Malicious File', tactic: 'Execution', description: 'An adversary may rely upon a user opening a malicious file in order to gain execution.' }
      ],

      // Persistence Techniques
      persistence: [
        { id: 'T1053.005', name: 'Scheduled Task', tactic: 'Persistence', description: 'Adversaries may abuse the Windows Task Scheduler to perform task scheduling for initial or recurring execution.' },
        { id: 'T1547.001', name: 'Registry Run Keys / Startup Folder', tactic: 'Persistence', description: 'Adversaries may achieve persistence by adding a program to a startup folder.' },
        { id: 'T1078', name: 'Valid Accounts', tactic: 'Persistence', description: 'Adversaries may obtain and abuse credentials of existing accounts.' },
        { id: 'T1543.003', name: 'Windows Service', tactic: 'Persistence', description: 'Adversaries may create or modify Windows services to repeatedly execute malicious payloads.' }
      ],

      // Credential Access Techniques
      credentialAccess: [
        { id: 'T1552.001', name: 'Credentials In Files', tactic: 'Credential Access', description: 'Adversaries may search local file systems and remote file shares for files containing insecurely stored credentials.' },
        { id: 'T1003.001', name: 'LSASS Memory', tactic: 'Credential Access', description: 'Adversaries may attempt to access credential material stored in the process memory of the Local Security Authority Subsystem Service.' },
        { id: 'T1110.001', name: 'Password Guessing', tactic: 'Credential Access', description: 'Adversaries may use a single or small list of commonly used passwords against many different accounts.' },
        { id: 'T1558.003', name: 'Kerberoasting', tactic: 'Credential Access', description: 'Adversaries may abuse a valid Kerberos ticket-granting ticket or sniff network traffic to obtain a ticket-granting service ticket.' }
      ],

      // Discovery Techniques
      discovery: [
        { id: 'T1018', name: 'Remote System Discovery', tactic: 'Discovery', description: 'Adversaries may attempt to get a listing of other systems by IP address, hostname, or other logical identifier.' },
        { id: 'T1083', name: 'File and Directory Discovery', tactic: 'Discovery', description: 'Adversaries may enumerate files and directories or may search in specific locations of a host or network share.' },
        { id: 'T1087.001', name: 'Local Account', tactic: 'Discovery', description: 'Adversaries may attempt to get a listing of local system accounts.' },
        { id: 'T1016', name: 'System Network Configuration Discovery', tactic: 'Discovery', description: 'Adversaries may look for details about the network configuration and settings.' }
      ],

      // Lateral Movement Techniques
      lateralMovement: [
        { id: 'T1021.001', name: 'Remote Desktop Protocol', tactic: 'Lateral Movement', description: 'Adversaries may use Valid Accounts to log into a computer using the Remote Desktop Protocol.' },
        { id: 'T1021.002', name: 'SMB/Windows Admin Shares', tactic: 'Lateral Movement', description: 'Adversaries may use Valid Accounts to interact with a remote network share using Server Message Block.' },
        { id: 'T1550.002', name: 'Pass the Hash', tactic: 'Lateral Movement', description: 'Adversaries may "pass the hash" using stolen password hashes to move laterally within an environment.' },
        { id: 'T1563.002', name: 'RDP Hijacking', tactic: 'Lateral Movement', description: 'Adversaries may hijack a legitimate user\'s remote desktop session to move laterally within an environment.' }
      ],

      // Collection Techniques
      collection: [
        { id: 'T1005', name: 'Data from Local System', tactic: 'Collection', description: 'Adversaries may search local system sources, such as file systems and configuration files or local databases.' },
        { id: 'T1039', name: 'Data from Network Shared Drive', tactic: 'Collection', description: 'Adversaries may search network shares on computers they have compromised to find files of interest.' },
        { id: 'T1560.001', name: 'Archive via Utility', tactic: 'Collection', description: 'Adversaries may use utilities to compress and/or encrypt collected data prior to exfiltration.' },
        { id: 'T1113', name: 'Screen Capture', tactic: 'Collection', description: 'Adversaries may attempt to take screen captures of the desktop to gather information over the course of an operation.' }
      ],

      // Exfiltration Techniques
      exfiltration: [
        { id: 'T1041', name: 'Exfiltration Over C2 Channel', tactic: 'Exfiltration', description: 'Adversaries may steal data by exfiltrating it over an existing command and control channel.' },
        { id: 'T1567.002', name: 'Exfiltration to Cloud Storage', tactic: 'Exfiltration', description: 'Adversaries may exfiltrate data to a cloud storage service rather than over their primary command and control channel.' },
        { id: 'T1048.003', name: 'Exfiltration Over Unencrypted Non-C2 Protocol', tactic: 'Exfiltration', description: 'Adversaries may steal data by exfiltrating it over an un-encrypted network protocol other than that of the existing command and control channel.' }
      ]
    };

    // Add URLs to all techniques
    Object.keys(database).forEach(category => {
      database[category] = database[category].map(technique => ({
        ...technique,
        url: this.generateMitreUrl(technique.id)
      }));
    });

    return database;
  }

  // Enhanced technique mapping based on asset types and vulnerabilities
  async getMitreAttackTechniques(assetType, vulnerabilities = []) {
    try {
      console.log(`[CTI] Getting enhanced MITRE ATT&CK techniques for asset type: ${assetType}`);

      const techniqueDb = this.getEnhancedTechniqueDatabase();
      let selectedTechniques = [];

      // Base techniques by asset type
      const assetTypeTechniques = {
        'Serveur': [
          ...techniqueDb.reconnaissance.slice(0, 2),
          ...techniqueDb.initialAccess.slice(0, 3),
          ...techniqueDb.execution.slice(0, 2),
          ...techniqueDb.persistence.slice(0, 2),
          ...techniqueDb.credentialAccess.slice(0, 2),
          ...techniqueDb.discovery.slice(0, 2),
          ...techniqueDb.lateralMovement.slice(0, 2),
          ...techniqueDb.collection.slice(0, 2),
          ...techniqueDb.exfiltration.slice(0, 2)
        ],
        'Base de données': [
          ...techniqueDb.reconnaissance.slice(0, 1),
          ...techniqueDb.initialAccess.slice(0, 2),
          ...techniqueDb.credentialAccess,
          ...techniqueDb.discovery.slice(1, 3),
          ...techniqueDb.collection,
          ...techniqueDb.exfiltration
        ],
        'Application': [
          ...techniqueDb.reconnaissance.slice(0, 2),
          ...techniqueDb.initialAccess,
          ...techniqueDb.execution,
          ...techniqueDb.persistence.slice(0, 2),
          ...techniqueDb.collection.slice(0, 2)
        ],
        'Réseau': [
          ...techniqueDb.reconnaissance,
          ...techniqueDb.credentialAccess.slice(0, 2),
          ...techniqueDb.discovery,
          ...techniqueDb.lateralMovement,
          ...techniqueDb.collection.slice(1, 3)
        ],
        'Équipement': [
          ...techniqueDb.reconnaissance.slice(0, 1),
          ...techniqueDb.initialAccess.slice(0, 2),
          ...techniqueDb.persistence.slice(0, 1),
          ...techniqueDb.discovery.slice(0, 2)
        ]
      };

      selectedTechniques = assetTypeTechniques[assetType] || [
        ...techniqueDb.reconnaissance.slice(0, 1),
        ...techniqueDb.initialAccess.slice(0, 2),
        ...techniqueDb.execution.slice(0, 1),
        ...techniqueDb.collection.slice(0, 1)
      ];

      // Enhance techniques based on vulnerabilities
      if (vulnerabilities && vulnerabilities.length > 0) {
        const vulnBasedTechniques = this.mapVulnerabilitiesToTechniques(vulnerabilities, techniqueDb);
        selectedTechniques = [...selectedTechniques, ...vulnBasedTechniques];
      }

      // Remove duplicates and limit to reasonable number
      const uniqueTechniques = selectedTechniques.filter((tech, index, self) =>
        index === self.findIndex(t => t.id === tech.id)
      ).slice(0, 15);

      console.log(`[CTI] Selected ${uniqueTechniques.length} techniques for ${assetType}`);
      return uniqueTechniques;

    } catch (error) {
      console.error(`[CTI] Error getting MITRE techniques:`, error);
      return [];
    }
  }

  // Map vulnerabilities to specific techniques
  mapVulnerabilitiesToTechniques(vulnerabilities, techniqueDb) {
    const additionalTechniques = [];

    vulnerabilities.forEach(vuln => {
      const description = vuln.description?.toLowerCase() || '';
      const severity = vuln.severity?.severity || '';

      // SQL Injection vulnerabilities
      if (description.includes('sql injection') || description.includes('sqli')) {
        additionalTechniques.push(
          { id: 'T1190', name: 'Exploit Public-Facing Application', tactic: 'Initial Access', description: 'SQL injection exploitation' },
          { id: 'T1005', name: 'Data from Local System', tactic: 'Collection', description: 'Database data extraction via SQL injection' }
        );
      }

      // Remote Code Execution vulnerabilities
      if (description.includes('remote code execution') || description.includes('rce')) {
        additionalTechniques.push(
          { id: 'T1190', name: 'Exploit Public-Facing Application', tactic: 'Initial Access', description: 'Remote code execution exploitation' },
          { id: 'T1059.003', name: 'Windows Command Shell', tactic: 'Execution', description: 'Command execution via RCE' }
        );
      }

      // Authentication bypass vulnerabilities
      if (description.includes('authentication bypass') || description.includes('auth bypass')) {
        additionalTechniques.push(
          { id: 'T1078', name: 'Valid Accounts', tactic: 'Initial Access', description: 'Authentication bypass exploitation' }
        );
      }

      // Directory traversal vulnerabilities
      if (description.includes('directory traversal') || description.includes('path traversal')) {
        additionalTechniques.push(
          { id: 'T1083', name: 'File and Directory Discovery', tactic: 'Discovery', description: 'Directory traversal exploitation' },
          { id: 'T1552.001', name: 'Credentials In Files', tactic: 'Credential Access', description: 'Credential file access via path traversal' }
        );
      }

      // High/Critical severity vulnerabilities get additional techniques
      if (severity === 'HIGH' || severity === 'CRITICAL') {
        additionalTechniques.push(
          ...techniqueDb.persistence.slice(0, 1),
          ...techniqueDb.lateralMovement.slice(0, 1)
        );
      }
    });

    return additionalTechniques;
  }

  // Main CTI analysis function
  async performCTIAnalysis(selectedAssets, analysisType = 'combined') {
    console.log(`[CTI] Starting ${analysisType} CTI analysis for assets:`, selectedAssets);

    const dataSourceMap = {
      'nist': 'NIST NVD',
      'euvd': 'EUVD (EU Vulnerability Database)',
      'mitre': 'MITRE ATT&CK',
      'atlas': 'MITRE ATLAS',
      'combined': 'NIST + MITRE ATT&CK'
    };

    const results = {
      totalVulnerabilities: 0,
      totalAttackTechniques: 0,
      overallRiskScore: 0,
      assets: [],
      analysisDate: new Date().toISOString(),
      dataSource: dataSourceMap[analysisType] || 'Combined Sources',
      analysisType: analysisType
    };

    try {
      // Process each asset
      for (const asset of selectedAssets) {
        console.log(`[CTI] Analyzing asset: ${asset.name} with ${analysisType} analysis`);

        let vulnerabilities = [];
        let attackTechniques = [];

        // Perform analysis based on type
        let searchMethod = 'none';
        if (analysisType === 'nist' || analysisType === 'euvd' || analysisType === 'combined') {
          // Get vulnerabilities from NIST or EUVD
          const searchResult = await this.searchVulnerabilities(asset, analysisType);
          if (searchResult && typeof searchResult === 'object' && searchResult.vulnerabilities) {
            vulnerabilities = searchResult.vulnerabilities;
            searchMethod = searchResult.searchMethod;
          } else {
            // Fallback for backward compatibility
            vulnerabilities = Array.isArray(searchResult) ? searchResult : [];
          }
        }

        if (analysisType === 'mitre' || analysisType === 'combined') {
          // Get live attack techniques from MITRE (based on asset product names and vulnerabilities)
          attackTechniques = await this.getLiveMitreAttackTechniques(asset, vulnerabilities);
        }

        if (analysisType === 'atlas') {
          // Get MITRE ATLAS AI/ML threats
          attackTechniques = await this.getAtlasThreats(asset);
        }

        // Calculate risk score for this asset
        const assetRiskScore = this.calculateAssetRiskScore(vulnerabilities, attackTechniques, asset.criticality);

        const assetResult = {
          ...asset,
          vulnerabilities: vulnerabilities,
          vulnerabilityCount: vulnerabilities.length,
          attackTechniques: attackTechniques,
          techniqueCount: attackTechniques.length,
          riskScore: assetRiskScore,
          searchMethod: searchMethod, // Track which search method was used
          highSeverityVulns: vulnerabilities.filter(v =>
            v.severity.severity === 'HIGH' || v.severity.severity === 'CRITICAL'
          ).length
        };

        results.assets.push(assetResult);
        results.totalVulnerabilities += vulnerabilities.length;
        results.totalAttackTechniques += attackTechniques.length;

        // Add delay to respect API rate limits
        if (analysisType === 'nist' || analysisType === 'combined') {
          await this.delay(6000); // 6 seconds between requests for NIST (strict rate limiting)
        } else if (analysisType === 'euvd') {
          await this.delay(2000); // 2 seconds between requests for EUVD (more lenient)
        } else {
          // Shorter delay for other analysis types
          await this.delay(1000); // 1 second between requests
        }
      }

      // Calculate overall risk score
      results.overallRiskScore = this.calculateOverallRiskScore(results.assets);

      console.log('[CTI] CTI analysis completed:', results);
      return results;

    } catch (error) {
      console.error('[CTI] Error during CTI analysis:', error);
      throw error;
    }
  }

  // Calculate risk score for individual asset
  calculateAssetRiskScore(vulnerabilities, attackTechniques, criticality) {
    let score = 0;

    // Base score from vulnerabilities
    vulnerabilities.forEach(vuln => {
      score += vuln.severity.score || 0;
    });

    // Add points for attack techniques
    score += attackTechniques.length * 0.5;

    // Multiply by criticality factor
    const criticalityMultiplier = {
      'critique': 1.5,
      'élevé': 1.3,
      'moyen': 1.0,
      'faible': 0.7
    };

    score *= criticalityMultiplier[criticality] || 1.0;

    // Normalize to 0-10 scale
    return Math.min(Math.round(score * 10) / 10, 10);
  }

  // Calculate overall risk score
  calculateOverallRiskScore(assets) {
    if (assets.length === 0) return 0;

    const totalScore = assets.reduce((sum, asset) => sum + asset.riskScore, 0);
    return Math.round((totalScore / assets.length) * 10) / 10;
  }

  // Fetch live MITRE ATT&CK data from GitHub repository
  async fetchLiveMitreData() {
    try {
      console.log('[CTI] Fetching live MITRE ATT&CK data...');

      // Check cache first
      const now = Date.now();
      if (this.mitreCache.data && this.mitreCache.lastUpdated &&
          (now - this.mitreCache.lastUpdated) < this.mitreCache.cacheExpiry) {
        console.log('[CTI] Using cached MITRE data');
        return this.mitreCache.data;
      }

      const response = await fetch(this.mitreStixUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
        }
      });

      if (!response.ok) {
        console.error(`[CTI] MITRE API error: ${response.status} ${response.statusText}`);
        return this.getFallbackMitreData();
      }

      const stixData = await response.json();
      console.log('[CTI] Successfully fetched MITRE STIX data');
      console.log('[CTI] MITRE STIX response structure:', {
        objectsCount: stixData.objects?.length || 0,
        firstObject: stixData.objects?.[0] || null,
        objectTypes: [...new Set(stixData.objects?.map(obj => obj.type) || [])]
      });

      // Parse STIX data to extract techniques
      const techniques = this.parseMitreStixData(stixData);

      // Update cache
      this.mitreCache.data = techniques;
      this.mitreCache.lastUpdated = now;

      console.log(`[CTI] Parsed ${techniques.length} techniques from live MITRE data`);
      return techniques;

    } catch (error) {
      console.error('[CTI] Error fetching live MITRE data:', error);
      return this.getFallbackMitreData();
    }
  }

  // Parse MITRE STIX data to extract techniques
  parseMitreStixData(stixData) {
    const techniques = [];

    if (!stixData.objects) {
      console.warn('[CTI] No objects found in MITRE STIX data');
      return this.getFallbackMitreData();
    }

    stixData.objects.forEach((obj, index) => {
      if (obj.type === 'attack-pattern' && !obj.revoked && !obj.x_mitre_deprecated) {
        // Log first few technique objects for structure analysis
        if (index < 3) {
          console.log(`[CTI] Raw MITRE technique object ${index + 1}:`, obj);
        }

        // Extract technique information
        const technique = {
          id: obj.external_references?.find(ref => ref.source_name === 'mitre-attack')?.external_id || 'Unknown',
          name: obj.name || 'Unknown Technique',
          description: obj.description || 'No description available',
          tactic: obj.kill_chain_phases?.map(phase => phase.phase_name).join(', ') || 'Unknown',
          platforms: obj.x_mitre_platforms || [],
          dataSource: obj.x_mitre_data_sources || [],
          version: obj.x_mitre_version || '1.0',
          lastModified: obj.modified || obj.created,
          url: this.generateMitreUrl(obj.external_references?.find(ref => ref.source_name === 'mitre-attack')?.external_id),
          // Enhanced fields from STIX data
          created: obj.created,
          modified: obj.modified,
          type: obj.type,
          spec_version: obj.spec_version,
          external_references: obj.external_references || [],
          kill_chain_phases: obj.kill_chain_phases || [],
          x_mitre_detection: obj.x_mitre_detection,
          x_mitre_is_subtechnique: obj.x_mitre_is_subtechnique,
          x_mitre_permissions_required: obj.x_mitre_permissions_required || [],
          x_mitre_effective_permissions: obj.x_mitre_effective_permissions || [],
          x_mitre_system_requirements: obj.x_mitre_system_requirements || [],
          x_mitre_impact_type: obj.x_mitre_impact_type || [],
          x_mitre_defense_bypassed: obj.x_mitre_defense_bypassed || [],
          x_mitre_remote_support: obj.x_mitre_remote_support,
          x_mitre_network_requirements: obj.x_mitre_network_requirements
        };

        // Log first processed technique for structure analysis
        if (index < 3) {
          console.log(`[CTI] Processed MITRE technique ${index + 1}:`, technique);
        }

        // Only include techniques with valid IDs
        if (technique.id.startsWith('T')) {
          techniques.push(technique);
        }
      }
    });

    return techniques;
  }

  // Fallback to enhanced static data if live fetch fails
  getFallbackMitreData() {
    console.log('[CTI] Using fallback enhanced MITRE data');
    const techniqueDb = this.getEnhancedTechniqueDatabase();

    return [
      ...techniqueDb.reconnaissance,
      ...techniqueDb.initialAccess,
      ...techniqueDb.execution,
      ...techniqueDb.persistence,
      ...techniqueDb.credentialAccess,
      ...techniqueDb.discovery,
      ...techniqueDb.lateralMovement,
      ...techniqueDb.collection,
      ...techniqueDb.exfiltration
    ];
  }

  // Enhanced technique selection using local MITRE API
  async getLiveMitreAttackTechniques(asset, vulnerabilities = []) {
    try {
      console.log(`[CTI] Getting MITRE ATT&CK techniques from local API for asset: ${asset.name}`);

      // Try local MITRE API first
      const localTechniques = await this.fetchFromLocalMitreApi(asset, vulnerabilities);
      if (localTechniques && localTechniques.length > 0) {
        console.log(`[CTI] Successfully retrieved ${localTechniques.length} techniques from local MITRE API`);
        return localTechniques;
      }

      console.log(`[CTI] Local MITRE API unavailable, falling back to GitHub STIX data`);
      // Fallback to GitHub STIX data
      const allTechniques = await this.fetchLiveMitreData();

      // Filter techniques by asset type and context
      const relevantTechniques = this.filterTechniquesByAssetType(allTechniques, asset.type);

      // Add vulnerability-specific techniques
      const vulnTechniques = this.mapVulnerabilitiesToLiveTechniques(vulnerabilities, allTechniques);

      // Combine and deduplicate
      const combinedTechniques = [...relevantTechniques, ...vulnTechniques];
      const uniqueTechniques = combinedTechniques.filter((tech, index, self) =>
        index === self.findIndex(t => t.id === tech.id)
      ).slice(0, 20); // Increased limit for more comprehensive coverage

      console.log(`[CTI] Selected ${uniqueTechniques.length} techniques from GitHub fallback`);
      return uniqueTechniques;

    } catch (error) {
      console.error(`[CTI] Error getting MITRE techniques:`, error);
      // Final fallback to enhanced static techniques
      return this.getMitreAttackTechniques(asset.type, vulnerabilities);
    }
  }

  // Fetch techniques from local MITRE API server
  async fetchFromLocalMitreApi(asset, vulnerabilities = []) {
    try {
      console.log(`[CTI] Fetching from local MITRE API for asset: ${asset.name}`);

      // Generate search keywords based on asset product names and vulnerabilities
      const searchKeywords = this.generateMitreSearchKeywords(asset, vulnerabilities);

      let allTechniques = [];

      // Search for each keyword
      for (const keyword of searchKeywords) {
        console.log(`[CTI] Searching local MITRE API with keyword: "${keyword}"`);

        const params = new URLSearchParams({
          q: keyword,
          type: 'attack-pattern',
          isSubtechnique: 'false' // Focus on main techniques first
        });

        const response = await fetch(`${this.localMitreAttackUrl}?${params}`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          timeout: 10000 // 10 second timeout
        });

        if (!response.ok) {
          console.warn(`[CTI] Local MITRE ATT&CK API error for keyword "${keyword}": ${response.status}`);
          continue;
        }

        const response_data = await response.json();

        // Handle backend proxy response format
        const data = response_data.success ? response_data.data : response_data;

        console.log(`[CTI] Local MITRE ATT&CK API response for "${keyword}":`, {
          success: response_data.success,
          source: response_data.source,
          resultsCount: Array.isArray(data) ? data.length : 0
        });

        // Process ATT&CK techniques (data is now a direct array)
        if (Array.isArray(data) && data.length > 0) {
          const techniques = data.map(tech => this.parseLocalMitreTechnique(tech));
          allTechniques = [...allTechniques, ...techniques];
        }
      }

      // Remove duplicates and limit results
      const uniqueTechniques = allTechniques.filter((tech, index, self) =>
        index === self.findIndex(t => t.id === tech.id)
      ).slice(0, 15);

      // Log technique diversity for debugging
      const techniqueIds = uniqueTechniques.map(t => t.id);
      const tacticCounts = {};
      uniqueTechniques.forEach(tech => {
        const tactic = tech.tactic || 'Unknown';
        tacticCounts[tactic] = (tacticCounts[tactic] || 0) + 1;
      });

      console.log(`[CTI] Local MITRE API returned ${uniqueTechniques.length} unique techniques`);
      console.log(`[CTI] Technique IDs:`, techniqueIds);
      console.log(`[CTI] Tactic distribution:`, tacticCounts);
      console.log(`[CTI] Sources:`, [...new Set(uniqueTechniques.map(t => t.source))]);

      return uniqueTechniques;

    } catch (error) {
      console.error(`[CTI] Error fetching from local MITRE API:`, error);
      return [];
    }
  }

  // Generate search keywords based on business asset product names
  generateMitreSearchKeywords(asset, vulnerabilities = []) {
    const keywords = [];

    // Extract product names from business asset
    const productKeywords = this.extractProductKeywords(asset);
    keywords.push(...productKeywords);

    // Add vulnerability-based keywords with more specificity
    vulnerabilities.forEach(vuln => {
      const description = vuln.description?.toLowerCase() || '';
      const vulnId = vuln.id?.toLowerCase() || '';

      // CVE-based keywords
      if (vulnId.includes('cve')) {
        if (description.includes('sql injection')) keywords.push('sql injection', 'database');
        if (description.includes('remote code execution')) keywords.push('code execution', 'exploit');
        if (description.includes('cross-site scripting')) keywords.push('web application', 'xss');
        if (description.includes('authentication')) keywords.push('credential access', 'authentication');
        if (description.includes('privilege escalation')) keywords.push('privilege escalation', 'elevation');
        if (description.includes('buffer overflow')) keywords.push('memory corruption', 'buffer overflow');
        if (description.includes('denial of service')) keywords.push('denial of service', 'dos');
        if (description.includes('information disclosure')) keywords.push('information disclosure', 'data leak');
      }
    });

    // Add tactical keywords for variety (fewer to prioritize product names)
    const tacticalKeywords = [
      'initial access', 'persistence', 'execution', 'defense evasion',
      'credential access', 'discovery', 'lateral movement', 'collection',
      'command and control', 'exfiltration', 'impact'
    ];

    // Randomly select 1-2 tactical keywords for variety
    const shuffledTactical = tacticalKeywords.sort(() => 0.5 - Math.random());
    keywords.push(...shuffledTactical.slice(0, 2));

    // Remove duplicates and limit to top keywords
    const uniqueKeywords = [...new Set(keywords)];

    console.log(`[CTI] Generated ${uniqueKeywords.length} keywords for ${asset.name}:`, uniqueKeywords);

    // Return keywords prioritizing product names
    return uniqueKeywords.slice(0, 8);
  }

  // Extract product keywords from business asset
  extractProductKeywords(asset) {
    const keywords = [];

    if (!asset) return keywords;

    // Extract from asset name
    if (asset.name) {
      const assetName = asset.name.toLowerCase();

      // Common product patterns
      const productPatterns = {
        // Web servers
        'apache': ['apache', 'httpd', 'web server'],
        'nginx': ['nginx', 'web server', 'reverse proxy'],
        'iis': ['iis', 'internet information services', 'microsoft web server'],
        'tomcat': ['tomcat', 'apache tomcat', 'java web server'],

        // Databases
        'mysql': ['mysql', 'database', 'sql'],
        'postgresql': ['postgresql', 'postgres', 'database'],
        'mongodb': ['mongodb', 'nosql', 'database'],
        'oracle': ['oracle', 'database', 'sql'],
        'sql server': ['sql server', 'microsoft sql', 'database'],

        // Operating Systems
        'windows': ['windows', 'microsoft', 'operating system'],
        'linux': ['linux', 'unix', 'operating system'],
        'ubuntu': ['ubuntu', 'linux', 'operating system'],
        'centos': ['centos', 'linux', 'operating system'],
        'debian': ['debian', 'linux', 'operating system'],

        // Applications
        'wordpress': ['wordpress', 'cms', 'web application'],
        'drupal': ['drupal', 'cms', 'web application'],
        'joomla': ['joomla', 'cms', 'web application'],
        'sharepoint': ['sharepoint', 'microsoft', 'collaboration'],
        'exchange': ['exchange', 'microsoft', 'email server'],
        'outlook': ['outlook', 'microsoft', 'email client'],

        // Network equipment
        'cisco': ['cisco', 'network', 'router', 'switch'],
        'juniper': ['juniper', 'network', 'firewall'],
        'fortinet': ['fortinet', 'firewall', 'security'],
        'palo alto': ['palo alto', 'firewall', 'security'],

        // Virtualization
        'vmware': ['vmware', 'virtualization', 'hypervisor'],
        'hyper-v': ['hyper-v', 'microsoft', 'virtualization'],
        'docker': ['docker', 'container', 'virtualization'],
        'kubernetes': ['kubernetes', 'container', 'orchestration']
      };

      // Check for product matches in asset name
      Object.entries(productPatterns).forEach(([product, terms]) => {
        if (assetName.includes(product)) {
          keywords.push(...terms);
          console.log(`[CTI] Detected product '${product}' in asset '${asset.name}'`);
        }
      });

      // Add the asset name itself as keywords (split by spaces/special chars)
      const nameWords = assetName
        .split(/[\s\-_.,;:()\[\]{}]+/)
        .filter(word => word.length > 2) // Filter out short words
        .slice(0, 3); // Limit to first 3 meaningful words

      keywords.push(...nameWords);
    }

    // Extract from asset type as fallback
    if (asset.type) {
      const assetType = asset.type.toLowerCase();
      const typeKeywords = {
        'serveur': ['server', 'service'],
        'base de données': ['database', 'data'],
        'application': ['application', 'software'],
        'réseau': ['network', 'infrastructure'],
        'équipement': ['device', 'equipment'],
        'logiciels': ['software', 'application']
      };

      if (typeKeywords[assetType]) {
        keywords.push(...typeKeywords[assetType]);
      }
    }

    // Final fallback: ensure we have at least some keywords
    if (keywords.length === 0) {
      console.log(`[CTI] No keywords generated for asset '${asset.name}', using generic fallback`);

      // Generic fallback keywords based on common IT terms
      const genericKeywords = ['system', 'application', 'service', 'software'];
      keywords.push(...genericKeywords);

      // Add asset type as keyword if available
      if (asset.type) {
        keywords.push(asset.type.toLowerCase());
      }
    }

    // Log the final keyword generation result
    const finalKeywords = [...new Set(keywords)]; // Remove duplicates
    console.log(`[CTI] Final keywords for '${asset.name}':`, finalKeywords);

    return finalKeywords;
  }

  // Parse technique from local MITRE API response
  parseLocalMitreTechnique(tech) {
    return {
      id: tech.external_references?.find(ref => ref.source_name === 'mitre-attack')?.external_id || tech.id,
      name: tech.name || 'Unknown Technique',
      description: tech.description || 'No description available',
      tactic: tech.kill_chain_phases?.map(phase => phase.phase_name).join(', ') || 'Unknown',
      platforms: tech.x_mitre_platforms || [],
      dataSource: tech.x_mitre_data_sources || [],
      url: this.generateMitreUrl(tech.external_references?.find(ref => ref.source_name === 'mitre-attack')?.external_id),
      source: 'Local MITRE API',
      created: tech.created,
      modified: tech.modified,
      isSubtechnique: tech.x_mitre_is_subtechnique || false
    };
  }

  // Parse ATLAS technique from local API response
  parseLocalAtlasTechnique(tech) {
    return {
      id: tech.external_references?.find(ref => ref.source_name === 'mitre-atlas')?.external_id || tech.id,
      name: tech.name || 'Unknown ATLAS Technique',
      description: tech.description || 'No description available',
      tactic: tech.kill_chain_phases?.map(phase => phase.phase_name).join(', ') || 'AI/ML Attack',
      platforms: ['AI/ML Systems'],
      dataSource: tech.x_mitre_data_sources || [],
      url: `https://atlas.mitre.org/techniques/${tech.external_references?.find(ref => ref.source_name === 'mitre-atlas')?.external_id}`,
      source: 'Local ATLAS API',
      created: tech.created,
      modified: tech.modified,
      isAtlas: true
    };
  }

  // Filter techniques by asset type using live data
  filterTechniquesByAssetType(allTechniques, assetType) {
    const assetTypeKeywords = {
      'Serveur': ['server', 'web', 'application', 'service', 'network', 'remote'],
      'Base de données': ['database', 'sql', 'data', 'credential', 'file'],
      'Application': ['application', 'web', 'client', 'execution', 'exploit'],
      'Réseau': ['network', 'remote', 'lateral', 'discovery', 'sniff'],
      'Équipement': ['system', 'endpoint', 'persistence', 'execution']
    };

    const keywords = assetTypeKeywords[assetType] || ['system', 'network'];

    return allTechniques.filter(tech => {
      const searchText = `${tech.name} ${tech.description} ${tech.tactic}`.toLowerCase();
      return keywords.some(keyword => searchText.includes(keyword));
    }).slice(0, 15);
  }

  // Map vulnerabilities to live techniques
  mapVulnerabilitiesToLiveTechniques(vulnerabilities, allTechniques) {
    const additionalTechniques = [];

    vulnerabilities.forEach(vuln => {
      const description = vuln.description?.toLowerCase() || '';

      // Find relevant techniques based on vulnerability type
      if (description.includes('sql injection')) {
        const sqlTechniques = allTechniques.filter(tech =>
          tech.name.toLowerCase().includes('exploit') ||
          tech.name.toLowerCase().includes('injection') ||
          tech.description.toLowerCase().includes('database')
        );
        additionalTechniques.push(...sqlTechniques.slice(0, 2));
      }

      if (description.includes('remote code execution')) {
        const rceTechniques = allTechniques.filter(tech =>
          tech.name.toLowerCase().includes('exploit') ||
          tech.name.toLowerCase().includes('execution') ||
          tech.tactic.toLowerCase().includes('execution')
        );
        additionalTechniques.push(...rceTechniques.slice(0, 2));
      }

      if (description.includes('authentication')) {
        const authTechniques = allTechniques.filter(tech =>
          tech.name.toLowerCase().includes('account') ||
          tech.name.toLowerCase().includes('credential') ||
          tech.tactic.toLowerCase().includes('credential')
        );
        additionalTechniques.push(...authTechniques.slice(0, 2));
      }
    });

    return additionalTechniques;
  }

  // Generate MITRE ATT&CK URL - always use main technique (remove sub-technique part)
  generateMitreUrl(techniqueId) {
    if (!techniqueId || !techniqueId.startsWith('T')) {
      return 'https://attack.mitre.org/techniques/';
    }

    // For sub-techniques (e.g., T1011.001), use only the main technique part (T1011)
    const mainTechniqueId = techniqueId.includes('.') ? techniqueId.split('.')[0] : techniqueId;

    return `https://attack.mitre.org/techniques/${mainTechniqueId}/`;
  }

  // Get MITRE ATLAS AI/ML threats using local API
  async getAtlasThreats(asset) {
    try {
      console.log(`[CTI] Getting MITRE ATLAS threats from local API for asset: ${asset.name}`);

      // Generate AI/ML specific search keywords
      const atlasKeywords = this.generateAtlasSearchKeywords(asset);

      let allAtlasThreats = [];

      // Search for each keyword in ATLAS data
      for (const keyword of atlasKeywords) {
        console.log(`[CTI] Searching local ATLAS API with keyword: "${keyword}"`);

        const params = new URLSearchParams({
          q: keyword,
          type: 'attack-pattern' // ATLAS uses same type as MITRE ATT&CK
        });

        const response = await fetch(`${this.localMitreAtlasUrl}?${params}`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          timeout: 10000
        });

        if (!response.ok) {
          console.warn(`[CTI] Local ATLAS API error for keyword "${keyword}": ${response.status}`);
          continue;
        }

        const response_data = await response.json();

        // Handle backend proxy response format
        const data = response_data.success ? response_data.data : response_data;

        console.log(`[CTI] Local ATLAS API response for "${keyword}":`, {
          success: response_data.success,
          source: response_data.source,
          resultsCount: Array.isArray(data) ? data.length : 0
        });

        // Process ATLAS techniques (data is now a direct array)
        if (Array.isArray(data) && data.length > 0) {
          const atlasTechniques = data.map(tech => this.parseLocalAtlasTechnique(tech));
          allAtlasThreats = [...allAtlasThreats, ...atlasTechniques];
        }
      }

      // Remove duplicates and limit results
      const uniqueAtlasThreats = allAtlasThreats.filter((tech, index, self) =>
        index === self.findIndex(t => t.id === tech.id)
      ).slice(0, 10); // Limit ATLAS threats

      // Log ATLAS technique diversity for debugging
      const atlasIds = uniqueAtlasThreats.map(t => t.id);
      const atlasTactics = {};
      uniqueAtlasThreats.forEach(tech => {
        const tactic = tech.tactic || 'Unknown';
        atlasTactics[tactic] = (atlasTactics[tactic] || 0) + 1;
      });

      console.log(`[CTI] Local ATLAS API returned ${uniqueAtlasThreats.length} unique threats`);
      console.log(`[CTI] ATLAS Technique IDs:`, atlasIds);
      console.log(`[CTI] ATLAS Tactic distribution:`, atlasTactics);
      console.log(`[CTI] ATLAS Sources:`, [...new Set(uniqueAtlasThreats.map(t => t.source))]);

      return uniqueAtlasThreats;

    } catch (error) {
      console.error(`[CTI] Error fetching ATLAS threats:`, error);
      // Fallback to static ATLAS threats
      return this.getStaticAtlasThreats(asset);
    }
  }

  // Generate search keywords for ATLAS based on asset type
  generateAtlasSearchKeywords(asset) {
    const keywords = [];

    // AI/ML specific keywords based on asset type with more variety
    const aiKeywords = {
      'application': ['machine learning', 'artificial intelligence', 'model', 'algorithm', 'neural network', 'deep learning'],
      'logiciels': ['ai system', 'ml model', 'neural network', 'deep learning', 'tensorflow', 'pytorch'],
      'serveur': ['ai service', 'ml inference', 'model serving', 'ai api', 'ml pipeline', 'model deployment'],
      'base de données': ['training data', 'dataset', 'data poisoning', 'model extraction', 'data pipeline', 'feature store'],
      'réseau': ['federated learning', 'distributed ai', 'model communication', 'edge computing'],
      'équipement': ['edge ai', 'embedded ml', 'iot ai', 'smart device', 'ai chip', 'gpu'],
      'equipements': ['ai hardware', 'ml accelerator', 'neural processor', 'edge device']
    };

    // Add asset type specific AI keywords
    const normalizedAssetType = asset.type?.toLowerCase() || '';
    if (aiKeywords[normalizedAssetType]) {
      keywords.push(...aiKeywords[normalizedAssetType]);
    }

    // Add general AI/ML attack keywords for variety
    const atlasAttackKeywords = [
      'adversarial', 'poisoning', 'evasion', 'extraction', 'inference',
      'backdoor', 'prompt injection', 'model stealing', 'membership inference'
    ];

    // Randomly select some attack keywords
    const shuffledAttacks = atlasAttackKeywords.sort(() => 0.5 - Math.random());
    keywords.push(...shuffledAttacks.slice(0, 2));

    // Add asset name based keywords if they contain AI/ML terms
    if (asset.name) {
      const name = asset.name.toLowerCase();
      if (name.includes('ai') || name.includes('ml') || name.includes('intelligence') ||
          name.includes('apprentissage') || name.includes('modèle') || name.includes('neural') ||
          name.includes('deep') || name.includes('learning')) {
        keywords.push('ai model', 'ml system', 'neural network');
      }
    }

    // Remove duplicates and increase limit for better variety
    const uniqueKeywords = [...new Set(keywords)];

    console.log(`[CTI] Generated ${uniqueKeywords.length} ATLAS keywords for ${asset.name}:`, uniqueKeywords);

    // Increased from 3 to 5 for better variety
    return uniqueKeywords.slice(0, 5);
  }

  // Static ATLAS threats as fallback
  getStaticAtlasThreats(asset) {
    console.log(`[CTI] Using static ATLAS threats for ${asset.name}`);

    return [
      {
        id: 'AML.T0043',
        name: 'Craft Adversarial Data',
        description: 'An adversary crafts adversarial data to evade a ML model or to cause a ML model to malfunction.',
        tactic: 'ML Attack Staging',
        platforms: ['AI/ML Systems'],
        url: 'https://atlas.mitre.org/techniques/AML.T0043',
        source: 'Static ATLAS',
        isAtlas: true
      },
      {
        id: 'AML.T0020',
        name: 'Poison Training Data',
        description: 'An adversary may poison the training data to cause a ML model to malfunction.',
        tactic: 'ML Attack Staging',
        platforms: ['AI/ML Systems'],
        url: 'https://atlas.mitre.org/techniques/AML.T0020',
        source: 'Static ATLAS',
        isAtlas: true
      },
      {
        id: 'AML.T0051',
        name: 'LLM Prompt Injection',
        description: 'An adversary may inject prompts into a large language model to cause unintended behavior.',
        tactic: 'ML Attack Staging',
        platforms: ['AI/ML Systems'],
        url: 'https://atlas.mitre.org/techniques/AML.T0051',
        source: 'Static ATLAS',
        isAtlas: true
      }
    ];
  }

  // Utility function for delays
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default new CTIService();
