import React, { useState } from 'react';
import { Save, Loader2, ChevronDown, ChevronUp, ExternalLink, Shield, Target } from 'lucide-react';

const CTIDisplay = ({ results, onSave, isSaving }) => {
    const [expandedAssets, setExpandedAssets] = useState({});
    const [expandedVulns, setExpandedVulns] = useState({});
    const [expandedTechniques, setExpandedTechniques] = useState({});

    if (!results || !results.assets || results.assets.length === 0) {
        return (
            <div className="text-center py-8">
                <div className="text-gray-500">Aucun résultat d'analyse disponible.</div>
            </div>
        );
    }

    const toggleAssetExpansion = (assetId) => {
        setExpandedAssets(prev => ({
            ...prev,
            [assetId]: !prev[assetId]
        }));
    };

    const toggleVulnExpansion = (vulnId) => {
        setExpandedVulns(prev => ({
            ...prev,
            [vulnId]: !prev[vulnId]
        }));
    };

    const toggleTechniqueExpansion = (techId) => {
        setExpandedTechniques(prev => ({
            ...prev,
            [techId]: !prev[techId]
        }));
    };

    // Helper function to safely render score
    const renderScore = (score) => {
        if (typeof score === 'object' && score !== null) {
            return score.score || score.baseScore || 'N/A';
        }
        return score || 'N/A';
    };

    // Helper function to safely render severity
    const renderSeverity = (severity) => {
        if (typeof severity === 'object' && severity !== null) {
            return severity.severity || severity.level || 'UNKNOWN';
        }
        return severity || 'UNKNOWN';
    };

    return (
        <div className="space-y-6">
            {/* Results Summary */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Résultats de l'Analyse CTI</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{results.totalVulnerabilities || 0}</div>
                        <div className="text-sm text-gray-600">Vulnérabilités</div>
                    </div>
                    <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">{results.totalAttackTechniques || 0}</div>
                        <div className="text-sm text-gray-600">Techniques d'Attaque</div>
                    </div>
                    <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{results.assets?.length || 0}</div>
                        <div className="text-sm text-gray-600">Actifs Analysés</div>
                    </div>
                </div>
            </div>

            {/* Assets Results */}
            {results.assets.map(asset => (
                <div key={asset.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                    <div
                        className="bg-gray-50 px-6 py-4 border-b border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors"
                        onClick={() => toggleAssetExpansion(asset.id)}
                    >
                        <div className="flex justify-between items-center">
                            <div>
                                <h4 className="text-lg font-semibold text-gray-800">{asset.name}</h4>
                                <p className="text-sm text-gray-600">{asset.type}</p>
                                <div className="flex items-center space-x-4 mt-2">
                                    {asset.vulnerabilities?.length > 0 && (
                                        <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">
                                            {asset.vulnerabilities.length} vulnérabilités
                                        </span>
                                    )}
                                    {asset.attackTechniques?.length > 0 && (
                                        <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
                                            {asset.attackTechniques.length} techniques
                                        </span>
                                    )}
                                </div>
                            </div>
                            {expandedAssets[asset.id] ?
                                <ChevronUp className="h-5 w-5 text-gray-500" /> :
                                <ChevronDown className="h-5 w-5 text-gray-500" />
                            }
                        </div>
                    </div>

                    {expandedAssets[asset.id] && (
                        <div className="p-6 space-y-6">
                            {/* Vulnerabilities */}
                            {asset.vulnerabilities?.length > 0 && (
                                <div>
                                    <h5 className="text-md font-semibold text-red-700 mb-3 flex items-center">
                                        <Shield className="h-4 w-4 mr-2" />
                                        Vulnérabilités ({asset.vulnerabilities.length})
                                    </h5>
                                    <div className="space-y-2">
                                        {asset.vulnerabilities.map(vuln => (
                                            <div key={vuln.id} className="bg-red-50 border border-red-200 rounded-lg">
                                                <div
                                                    className="p-4 cursor-pointer hover:bg-red-100 transition-colors"
                                                    onClick={() => toggleVulnExpansion(vuln.id)}
                                                >
                                                    <div className="flex justify-between items-start">
                                                        <div className="flex-1">
                                                            <div className="flex items-center justify-between">
                                                                <h6 className="font-semibold text-red-800">{vuln.id}</h6>
                                                                {expandedVulns[vuln.id] ?
                                                                    <ChevronUp className="h-4 w-4 text-red-600" /> :
                                                                    <ChevronDown className="h-4 w-4 text-red-600" />
                                                                }
                                                            </div>
                                                            <div className="flex items-center mt-2 space-x-4">
                                                                <span className={`px-2 py-1 rounded text-xs font-medium ${
                                                                    renderSeverity(vuln.severity) === 'CRITICAL' ? 'bg-red-100 text-red-800' :
                                                                    renderSeverity(vuln.severity) === 'HIGH' ? 'bg-orange-100 text-orange-800' :
                                                                    renderSeverity(vuln.severity) === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                                                                    'bg-green-100 text-green-800'
                                                                }`}>
                                                                    {renderSeverity(vuln.severity)}
                                                                </span>
                                                                <span className="text-xs text-gray-500">
                                                                    Score: {renderScore(vuln.score)}
                                                                </span>
                                                                {vuln.url && (
                                                                    <a
                                                                        href={vuln.url}
                                                                        target="_blank"
                                                                        rel="noopener noreferrer"
                                                                        className="text-blue-600 hover:text-blue-800 text-xs flex items-center"
                                                                        onClick={(e) => e.stopPropagation()}
                                                                    >
                                                                        <ExternalLink className="h-3 w-3 mr-1" />
                                                                        Détails
                                                                    </a>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {expandedVulns[vuln.id] && (
                                                    <div className="px-4 pb-4 border-t border-red-200 bg-red-25">
                                                        <div className="space-y-3 mt-3">
                                                            <p className="text-sm text-gray-700">{vuln.description}</p>

                                                            {/* Enhanced vulnerability details */}
                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                                                                <div>
                                                                    <span className="font-semibold text-gray-600">Published:</span>
                                                                    <span className="ml-2">{new Date(vuln.publishedDate).toLocaleDateString()}</span>
                                                                </div>
                                                                <div>
                                                                    <span className="font-semibold text-gray-600">Last Modified:</span>
                                                                    <span className="ml-2">{new Date(vuln.lastModified).toLocaleDateString()}</span>
                                                                </div>
                                                                <div>
                                                                    <span className="font-semibold text-gray-600">Status:</span>
                                                                    <span className="ml-2">{vuln.vulnStatus}</span>
                                                                </div>
                                                                <div>
                                                                    <span className="font-semibold text-gray-600">Source:</span>
                                                                    <span className="ml-2">{vuln.sourceIdentifier}</span>
                                                                </div>
                                                            </div>

                                                            {/* CVSS Details */}
                                                            {vuln.cvssVector && (
                                                                <div className="bg-gray-50 p-2 rounded">
                                                                    <div className="text-xs font-semibold text-gray-600 mb-1">CVSS Vector:</div>
                                                                    <div className="text-xs font-mono text-gray-700">{vuln.cvssVector}</div>
                                                                    <div className="grid grid-cols-2 gap-2 mt-2 text-xs">
                                                                        <div>Exploitability: {vuln.exploitabilityScore}</div>
                                                                        <div>Impact: {vuln.impactScore}</div>
                                                                    </div>
                                                                </div>
                                                            )}

                                                            {/* Weaknesses */}
                                                            {vuln.weaknesses && vuln.weaknesses.length > 0 && (
                                                                <div>
                                                                    <div className="text-xs font-semibold text-gray-600 mb-1">Weaknesses:</div>
                                                                    <div className="space-y-1">
                                                                        {vuln.weaknesses.map((weakness, idx) => (
                                                                            <div key={idx} className="text-xs bg-yellow-50 p-1 rounded">
                                                                                <span className="font-medium">{weakness.type}:</span> {weakness.description}
                                                                            </div>
                                                                        ))}
                                                                    </div>
                                                                </div>
                                                            )}

                                                            {/* Affected Configurations */}
                                                            {vuln.configurations && vuln.configurations.length > 0 && (
                                                                <div>
                                                                    <div className="text-xs font-semibold text-gray-600 mb-1">Affected Products:</div>
                                                                    <div className="space-y-1 max-h-20 overflow-y-auto">
                                                                        {vuln.configurations.map((config, idx) => (
                                                                            <div key={idx} className="text-xs bg-blue-50 p-1 rounded">
                                                                                <div className="font-mono text-blue-800">{config.criteria}</div>
                                                                                {config.versionStartIncluding && (
                                                                                    <div className="text-blue-600">
                                                                                        Versions: {config.versionStartIncluding} - {config.versionEndExcluding || 'latest'}
                                                                                    </div>
                                                                                )}
                                                                            </div>
                                                                        ))}
                                                                    </div>
                                                                </div>
                                                            )}

                                                            {/* References */}
                                                            {vuln.references && vuln.references.length > 0 && (
                                                                <div>
                                                                    <div className="text-xs font-semibold text-gray-600 mb-1">References:</div>
                                                                    <div className="space-y-1">
                                                                        {vuln.references.slice(0, 3).map((ref, idx) => (
                                                                            <a
                                                                                key={idx}
                                                                                href={ref.url}
                                                                                target="_blank"
                                                                                rel="noopener noreferrer"
                                                                                className="block text-xs text-blue-600 hover:text-blue-800 truncate"
                                                                            >
                                                                                {ref.url}
                                                                            </a>
                                                                        ))}
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* Attack Techniques */}
                            {asset.attackTechniques?.length > 0 && (
                                <div>
                                    <h5 className="text-md font-semibold text-purple-700 mb-3 flex items-center">
                                        <Target className="h-4 w-4 mr-2" />
                                        Techniques d'Attaque ({asset.attackTechniques.length})
                                    </h5>
                                    <div className="space-y-2">
                                        {asset.attackTechniques.map(technique => (
                                            <div key={technique.id} className="bg-purple-50 border border-purple-200 rounded-lg">
                                                <div
                                                    className="p-4 cursor-pointer hover:bg-purple-100 transition-colors"
                                                    onClick={() => toggleTechniqueExpansion(technique.id)}
                                                >
                                                    <div className="flex justify-between items-start">
                                                        <div className="flex-1">
                                                            <div className="flex items-center justify-between">
                                                                <h6 className="font-semibold text-purple-800">
                                                                    {technique.id} - {technique.name}
                                                                </h6>
                                                                {expandedTechniques[technique.id] ?
                                                                    <ChevronUp className="h-4 w-4 text-purple-600" /> :
                                                                    <ChevronDown className="h-4 w-4 text-purple-600" />
                                                                }
                                                            </div>
                                                            <div className="flex items-center mt-2 space-x-2 flex-wrap">
                                                                {technique.tactic && (
                                                                    <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs font-medium">
                                                                        {technique.tactic}
                                                                    </span>
                                                                )}
                                                                {technique.x_mitre_is_subtechnique && (
                                                                    <span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-xs font-medium">
                                                                        Sub-technique
                                                                    </span>
                                                                )}
                                                                {technique.platforms && technique.platforms.length > 0 && (
                                                                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">
                                                                        {technique.platforms.join(', ')}
                                                                    </span>
                                                                )}
                                                                {technique.version && (
                                                                    <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs font-medium">
                                                                        v{technique.version}
                                                                    </span>
                                                                )}
                                                                {(technique.url || technique.link) && (
                                                                    <a
                                                                        href={technique.url || technique.link || `https://attack.mitre.org/techniques/${technique.id}/`}
                                                                        target="_blank"
                                                                        rel="noopener noreferrer"
                                                                        className="text-blue-600 hover:text-blue-800 text-xs flex items-center"
                                                                        onClick={(e) => e.stopPropagation()}
                                                                    >
                                                                        <ExternalLink className="h-3 w-3 mr-1" />
                                                                        {technique.source === 'atlas' || technique.type === 'atlas' ? 'ATLAS' : 'MITRE'}
                                                                    </a>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {expandedTechniques[technique.id] && (
                                                    <div className="px-4 pb-4 border-t border-purple-200 bg-purple-25">
                                                        <div className="space-y-3 mt-3">
                                                            <p className="text-sm text-gray-700">{technique.description}</p>

                                                            {/* Enhanced technique details */}
                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                                                                <div>
                                                                    <span className="font-semibold text-gray-600">Created:</span>
                                                                    <span className="ml-2">{technique.created ? new Date(technique.created).toLocaleDateString() : 'N/A'}</span>
                                                                </div>
                                                                <div>
                                                                    <span className="font-semibold text-gray-600">Last Modified:</span>
                                                                    <span className="ml-2">{technique.lastModified ? new Date(technique.lastModified).toLocaleDateString() : 'N/A'}</span>
                                                                </div>
                                                                <div>
                                                                    <span className="font-semibold text-gray-600">Version:</span>
                                                                    <span className="ml-2">{technique.version || 'N/A'}</span>
                                                                </div>
                                                                <div>
                                                                    <span className="font-semibold text-gray-600">Sub-technique:</span>
                                                                    <span className="ml-2">{technique.x_mitre_is_subtechnique ? 'Yes' : 'No'}</span>
                                                                </div>
                                                            </div>

                                                            {/* Platforms */}
                                                            {technique.platforms && technique.platforms.length > 0 && (
                                                                <div>
                                                                    <div className="text-xs font-semibold text-gray-600 mb-1">Platforms:</div>
                                                                    <div className="flex flex-wrap gap-1">
                                                                        {technique.platforms.map((platform, idx) => (
                                                                            <span key={idx} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">
                                                                                {platform}
                                                                            </span>
                                                                        ))}
                                                                    </div>
                                                                </div>
                                                            )}

                                                            {/* Kill Chain Phases (Tactics) */}
                                                            {technique.kill_chain_phases && technique.kill_chain_phases.length > 0 && (
                                                                <div>
                                                                    <div className="text-xs font-semibold text-gray-600 mb-1">Tactics:</div>
                                                                    <div className="flex flex-wrap gap-1">
                                                                        {technique.kill_chain_phases.map((phase, idx) => (
                                                                            <span key={idx} className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs font-medium">
                                                                                {phase.phase_name?.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                                                            </span>
                                                                        ))}
                                                                    </div>
                                                                </div>
                                                            )}

                                                            {/* Data Sources */}
                                                            {technique.dataSource && technique.dataSource.length > 0 && (
                                                                <div>
                                                                    <div className="text-xs font-semibold text-gray-600 mb-1">Data Sources:</div>
                                                                    <div className="space-y-1">
                                                                        {technique.dataSource.slice(0, 3).map((source, idx) => (
                                                                            <div key={idx} className="text-xs bg-green-50 p-1 rounded">
                                                                                {source}
                                                                            </div>
                                                                        ))}
                                                                    </div>
                                                                </div>
                                                            )}

                                                            {/* Detection Information */}
                                                            {technique.x_mitre_detection && (
                                                                <div>
                                                                    <div className="text-xs font-semibold text-gray-600 mb-1">Detection:</div>
                                                                    <div className="text-xs bg-yellow-50 p-2 rounded border border-yellow-200">
                                                                        <p className="text-gray-700">{technique.x_mitre_detection}</p>
                                                                    </div>
                                                                </div>
                                                            )}

                                                            {/* Permissions Required */}
                                                            {technique.x_mitre_permissions_required && technique.x_mitre_permissions_required.length > 0 && (
                                                                <div>
                                                                    <div className="text-xs font-semibold text-gray-600 mb-1">Permissions Required:</div>
                                                                    <div className="flex flex-wrap gap-1">
                                                                        {technique.x_mitre_permissions_required.map((perm, idx) => (
                                                                            <span key={idx} className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs font-medium">
                                                                                {perm}
                                                                            </span>
                                                                        ))}
                                                                    </div>
                                                                </div>
                                                            )}

                                                            {/* Defense Bypassed */}
                                                            {technique.x_mitre_defense_bypassed && technique.x_mitre_defense_bypassed.length > 0 && (
                                                                <div>
                                                                    <div className="text-xs font-semibold text-gray-600 mb-1">Defense Bypassed:</div>
                                                                    <div className="flex flex-wrap gap-1">
                                                                        {technique.x_mitre_defense_bypassed.map((defense, idx) => (
                                                                            <span key={idx} className="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs font-medium">
                                                                                {defense}
                                                                            </span>
                                                                        ))}
                                                                    </div>
                                                                </div>
                                                            )}

                                                            {/* External References */}
                                                            {technique.external_references && technique.external_references.length > 1 && (
                                                                <div>
                                                                    <div className="text-xs font-semibold text-gray-600 mb-1">References:</div>
                                                                    <div className="space-y-1 max-h-20 overflow-y-auto">
                                                                        {technique.external_references.slice(1, 4).map((ref, idx) => (
                                                                            <div key={idx} className="text-xs">
                                                                                {ref.url ? (
                                                                                    <a
                                                                                        href={ref.url}
                                                                                        target="_blank"
                                                                                        rel="noopener noreferrer"
                                                                                        className="text-blue-600 hover:text-blue-800 block truncate"
                                                                                    >
                                                                                        {ref.source_name || ref.description || ref.url}
                                                                                    </a>
                                                                                ) : (
                                                                                    <span className="text-gray-600">{ref.source_name || ref.description}</span>
                                                                                )}
                                                                            </div>
                                                                        ))}
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            ))}

            {/* Save Button */}
            <div className="flex justify-center pt-6">
                <button
                    onClick={onSave}
                    disabled={isSaving}
                    className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    {isSaving ? (
                        <>
                            <Loader2 className="animate-spin -ml-1 mr-3 h-5 w-5" />
                            Sauvegarde...
                        </>
                    ) : (
                        <>
                            <Save className="-ml-1 mr-3 h-5 w-5" />
                            Sauvegarder les Résultats
                        </>
                    )}
                </button>
            </div>
        </div>
    );
};

export default CTIDisplay;
